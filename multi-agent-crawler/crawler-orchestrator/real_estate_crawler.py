#!/usr/bin/env python3
"""
Real Estate Data Crawler for Poland
Main script to run the multi-agent real estate data gathering system.
"""

import os
import sys
import logging
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.real_estate_crew import RealEstateCrew
from src.services.llm_service import LLMService
from src.services.redis_service import RedisService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("real_estate_crawler.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Real Estate Data Crawler for Poland')
    
    parser.add_argument(
        '--region', 
        type=str, 
        default='Poland',
        help='Target region for data gathering (default: Poland)'
    )
    
    parser.add_argument(
        '--max-sites', 
        type=int, 
        default=20,
        help='Maximum number of sites to crawl (default: 20)'
    )
    
    parser.add_argument(
        '--output-dir', 
        type=str, 
        default='./data',
        help='Directory to store output data (default: ./data)'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true',
        help='Enable verbose output'
    )
    
    return parser.parse_args()

def setup_environment():
    """Set up the environment for the crawler."""
    # Load environment variables
    load_dotenv()
    
    # Check for required environment variables
    required_vars = ['OPENAI_API_KEY', 'REDIS_URL']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables in your .env file or environment")
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    args = parse_arguments()
    os.makedirs(args.output_dir, exist_ok=True)
    
    return args

def main():
    """Main function to run the real estate data crawler."""
    try:
        # Set up environment and parse arguments
        args = setup_environment()
        
        # Configure logging level based on verbose flag
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        
        logger.info("Starting Real Estate Data Crawler")
        logger.info(f"Target Region: {args.region}")
        logger.info(f"Max Sites: {args.max_sites}")
        logger.info(f"Output Directory: {args.output_dir}")
        
        # Initialize services
        llm_service = LLMService()
        redis_service = RedisService()
        
        # Create and run the real estate crew
        start_time = datetime.now()
        logger.info(f"Crawler started at: {start_time}")
        
        crew = RealEstateCrew(llm_service=llm_service)
        result = crew.run(target_region=args.region, max_sites=args.max_sites)
        
        # Save results
        end_time = datetime.now()
        duration = end_time - start_time
        
        timestamp = end_time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(args.output_dir, f"real_estate_data_{timestamp}.json")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)
        
        logger.info(f"Crawler completed in: {duration}")
        logger.info(f"Results saved to: {output_file}")
        
        return 0
    
    except KeyboardInterrupt:
        logger.info("Crawler interrupted by user")
        return 1
    
    except Exception as e:
        logger.exception(f"Error running crawler: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
