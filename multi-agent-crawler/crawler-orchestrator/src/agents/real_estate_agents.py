"""
Real Estate Data Gathering Agents for Poland
This module defines specialized agents for crawling and extracting real estate data.
"""

import os
from typing import List, Dict, Any
from crewai import Agent, Task, Crew
from langchain.tools import BaseTool
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class RealEstateAgents:
    """Factory class for creating specialized real estate data gathering agents."""
    
    def __init__(self, llm_service, tools: List[BaseTool] = None):
        """Initialize the agent factory with LLM service and tools."""
        self.llm_service = llm_service
        self.tools = tools or []
    
    def create_discovery_agent(self) -> Agent:
        """Creates an agent specialized in discovering real estate websites and resources."""
        return Agent(
            role="Real Estate Resource Discovery Specialist",
            goal="Identify comprehensive sources of real estate and company data across Poland",
            backstory="""You are an expert at finding real estate websites, property listings, 
            company directories, and business registries in Poland. You have extensive knowledge 
            of the Polish real estate market and know where to find the most valuable data sources.""",
            verbose=True,
            llm=self.llm_service.get_llm(),
            tools=self.tools,
            allow_delegation=True
        )
    
    def create_crawling_agent(self) -> Agent:
        """Creates an agent specialized in efficient web crawling."""
        return Agent(
            role="Advanced Web Crawler Specialist",
            goal="Systematically crawl identified websites to gather comprehensive real estate data",
            backstory="""You are a relentless web crawler expert who knows how to navigate complex 
            websites, bypass anti-scraping measures, and efficiently extract data. You understand 
            website structures and can crawl through pagination, search results, and detailed listings.""",
            verbose=True,
            llm=self.llm_service.get_llm(),
            tools=self.tools,
            allow_delegation=True
        )
    
    def create_extraction_agent(self) -> Agent:
        """Creates an agent specialized in extracting structured data from websites."""
        return Agent(
            role="Data Extraction Specialist",
            goal="Extract accurate and structured real estate and company data from crawled content",
            backstory="""You are an expert at parsing HTML and extracting structured information. 
            You can identify patterns in websites and extract addresses, phone numbers, company names, 
            property details, and other relevant information with high precision.""",
            verbose=True,
            llm=self.llm_service.get_llm(),
            tools=self.tools,
            allow_delegation=True
        )
    
    def create_validation_agent(self) -> Agent:
        """Creates an agent specialized in validating extracted data."""
        return Agent(
            role="Data Validation Specialist",
            goal="Ensure all extracted data is accurate, complete, and properly formatted",
            backstory="""You are a meticulous data validator who ensures information accuracy. 
            You can verify Polish addresses, phone numbers, and company details. You know how to 
            cross-reference information from multiple sources to confirm its validity.""",
            verbose=True,
            llm=self.llm_service.get_llm(),
            tools=self.tools,
            allow_delegation=True
        )
    
    def create_storage_agent(self) -> Agent:
        """Creates an agent specialized in organizing and storing collected data."""
        return Agent(
            role="Data Storage and Organization Specialist",
            goal="Efficiently store and organize all collected real estate data",
            backstory="""You are an expert at data organization and storage. You know how to 
            structure databases for real estate information, create efficient indexing systems, 
            and ensure data is stored in a way that makes it easily searchable and retrievable.""",
            verbose=True,
            llm=self.llm_service.get_llm(),
            tools=self.tools,
            allow_delegation=True
        )
    
    def create_orchestration_agent(self) -> Agent:
        """Creates an agent specialized in coordinating the entire data gathering process."""
        return Agent(
            role="Real Estate Data Gathering Orchestrator",
            goal="Coordinate the entire data gathering process for maximum efficiency and coverage",
            backstory="""You are a strategic coordinator who oversees complex data gathering operations. 
            You know how to prioritize targets, allocate resources efficiently, and ensure comprehensive 
            coverage of the Polish real estate market. You can adapt strategies based on results and 
            overcome obstacles.""",
            verbose=True,
            llm=self.llm_service.get_llm(),
            tools=self.tools,
            allow_delegation=False
        )
