"""
Specialized tools for extracting real estate and company data from Polish websites.
"""

import re
from typing import List, Dict, Any
from langchain.tools import BaseTool
from bs4 import BeautifulSoup

class PolishAddressExtractor(BaseTool):
    name = "polish_address_extractor"
    description = "Extracts Polish addresses from text using postal code and street patterns."

    def _run(self, text: str) -> List[str]:
        # Polish postal code: dd-ddd, e.g., 00-001
        postal_pattern = r"\d{2}-\d{3}"
        # Common street prefixes: ul., al., pl., os.
        street_pattern = r"(ul\.|al\.|pl\.|os\.)\s+[A-ZĄĆĘŁŃÓŚŹŻ][\w\s\.\-]+"
        # Combine patterns
        address_pattern = rf"({street_pattern}.*?{postal_pattern}.*?\b[A-ZĄĆĘŁŃÓŚŹŻa-ząćęłńóśźż\- ]+\b)"
        matches = re.findall(address_pattern, text)
        return [m for m in matches] if matches else []

class PolishPhoneExtractor(BaseTool):
    name = "polish_phone_extractor"
    description = "Extracts Polish phone numbers from text."

    def _run(self, text: str) -> List[str]:
        # Polish phone: +48 xxx xxx xxx or 48 xxx xxx xxx or xxx xxx xxx
        phone_pattern = r"(\+48\s?\d{3}[\s\-]?\d{3}[\s\-]?\d{3}|\b\d{3}[\s\-]?\d{3}[\s\-]?\d{3}\b)"
        return re.findall(phone_pattern, text)

class CompanyNameExtractor(BaseTool):
    name = "company_name_extractor"
    description = "Extracts company names from HTML using common tags and schema.org microdata."

    def _run(self, html: str) -> List[str]:
        soup = BeautifulSoup(html, "html.parser")
        names = set()
        # Try schema.org
        for tag in soup.find_all(attrs={"itemprop": "name"}):
            if tag.text.strip():
                names.add(tag.text.strip())
        # Try <h1>, <h2>, <title>
        for tag in soup.find_all(["h1", "h2", "title"]):
            if tag.text.strip():
                names.add(tag.text.strip())
        # Try meta og:site_name
        meta = soup.find("meta", property="og:site_name")
        if meta and meta.get("content"):
            names.add(meta["content"].strip())
        return list(names)

class RealEstateDataExtractor(BaseTool):
    name = "real_estate_data_extractor"
    description = "Extracts addresses, phone numbers, and company names from HTML content."

    def _run(self, html: str) -> List[Dict[str, Any]]:
        soup = BeautifulSoup(html, "html.parser")
        text = soup.get_text(separator=" ", strip=True)
        addresses = PolishAddressExtractor()._run(text)
        phones = PolishPhoneExtractor()._run(text)
        names = CompanyNameExtractor()._run(html)
        return [{
            "addresses": addresses,
            "phones": phones,
            "company_names": names
        }]
