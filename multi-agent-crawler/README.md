# 🕷️ Multi-Agent Crawler Orchestrator

Advanced web crawling and research platform powered by AI agents, featuring Crawl4AI, Exa API, Brave Search, CrewAI, and NocoDB integration.

## ✨ Features

### 🤖 AI-Powered Multi-Agent System
- **Research Agent**: Conducts comprehensive web research using multiple search engines
- **Content Analyzer**: Extracts insights, sentiment, and entities from crawled content
- **Data Curator**: Organizes, cleans, and structures collected data
- **Report Generator**: Creates comprehensive reports from analyzed data

### 🔍 Advanced Search Capabilities
- **Exa API**: Semantic search for contextually relevant content
- **Brave Search**: Traditional web search with privacy focus
- **Combined Search**: Intelligent merging of results from multiple sources
- **Semantic Search**: Find content based on meaning, not just keywords

### 🕷️ Powerful Web Crawling
- **Crawl4AI Integration**: Advanced web crawling with JavaScript support
- **Concurrent Crawling**: Process multiple URLs simultaneously
- **Content Extraction**: Extract clean text, images, and structured data
- **Pagination Support**: Automatically crawl multi-page content

### 📊 Data Management
- **NocoDB Integration**: Store and manage crawled data in a visual database
- **Redis Caching**: Fast caching for improved performance
- **Data Deduplication**: Automatic removal of duplicate content
- **Structured Storage**: Organized data storage with metadata

### 🎯 Research Missions
- **Objective-Driven Research**: Define specific research goals and objectives
- **Automated Workflows**: AI agents work together to complete research tasks
- **Comprehensive Reports**: Generate detailed reports with insights and recommendations
- **Background Processing**: Long-running research missions with progress tracking

### 🖥️ User Interfaces
- **Streamlit Control Panel**: Advanced agent management and monitoring interface
- **Gradio Web Interface**: User-friendly web interface for all operations
- **FastAPI REST API**: Programmatic access to all functionality
- **Real-time Monitoring**: Track system status and performance
- **Interactive Documentation**: Auto-generated API documentation

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- API Keys for:
  - [OpenRouter](https://openrouter.ai/) (for LLM access)
  - [Exa](https://exa.ai/) (for semantic search)
  - [Brave Search](https://brave.com/search/api/) (for web search)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd multi-agent-crawler
   ```

2. **Run the installation script**
   ```bash
   chmod +x install.sh
   ./install.sh
   ```

3. **Configure API keys**
   Edit the `.env` file with your API keys:
   ```bash
   OPENROUTER_API_KEY=your_openrouter_key_here
   EXA_API_KEY=your_exa_key_here
   BRAVE_API_KEY=your_brave_key_here
   ```

4. **Start the services**
   ```bash
   docker-compose up -d
   ```

### Access Points

- **📱 Tablet Interface**: http://localhost:8004 *(Optimized for iPad/Android)*
- **🎛️ Streamlit Control Panel**: http://localhost:8003
- **🕷️ Gradio Interface**: http://localhost:8001
- **🔧 FastAPI Docs**: http://localhost:8000/docs
- **📊 NocoDB**: http://localhost:8080
- **🔍 Crawl4AI Service**: http://localhost:8002

### System Requirements

**Optimized for:**
- **CPU**: 4 vCPU minimum
- **RAM**: 16GB minimum
- **Storage**: 20GB available space
- **Network**: Stable internet connection for API calls

**Performance Targets:**
- Memory usage: <2GB during normal operations
- CPU utilization: <50% during normal operations
- Response time: <2 seconds for most operations

## 📦 Latest Package Versions (Updated 2025)

This project has been updated with the latest package versions for optimal performance and security:

### Core Framework Updates
- **FastAPI**: 0.115.12 (latest stable with enhanced performance)
- **Uvicorn**: 0.32.1 (latest with improved ASGI handling)
- **Pydantic**: 2.10.3 (latest v2 with enhanced validation)

### AI & Crawling Updates
- **CrewAI**: 0.126.0 (latest with enhanced multi-agent capabilities)
- **Crawl4AI**: 0.6.3 (latest with improved crawling performance)
- **LangChain**: 0.3.12 (latest stable with better tool integration)
- **Playwright**: 1.48.0 (latest browser automation with new features)

### Data & Processing Updates
- **Pandas**: 2.2.3 (latest with performance optimizations)
- **NumPy**: 2.2.1 (latest with enhanced array operations)
- **Redis**: 5.2.1 (latest with improved memory management)
- **SQLAlchemy**: 2.0.36 (latest with async improvements)

### UI & Visualization Updates
- **Gradio**: 5.9.1 (latest with enhanced UI components)
- **Streamlit**: 1.41.0 (latest with improved performance)
- **Plotly**: 5.24.1 (latest with new chart types)

### Testing & Development
- **Pytest**: 8.3.4 (latest with improved test discovery)
- **HTTPx**: 0.28.1 (latest async HTTP client)

## 📖 Usage

### Research Mission Example

1. **Via Gradio Interface**:
   - Open http://localhost:8001
   - Go to "Research Mission" tab
   - Enter your research topic and objectives
   - Click "Start Research Mission"

2. **Via API**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/research/mission" \
        -H "Content-Type: application/json" \
        -d '{
          "topic": "Artificial Intelligence in Healthcare",
          "objectives": [
            "Find recent developments in AI-powered diagnostics",
            "Identify key companies and researchers in the field",
            "Analyze market trends and future predictions"
          ]
        }'
   ```

### Web Search Example

```bash
curl -X POST "http://localhost:8000/api/v1/search" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "machine learning trends 2024",
       "num_results": 10,
       "use_exa": true,
       "use_brave": true
     }'
```

### Web Crawling Example

```bash
curl -X POST "http://localhost:8000/api/v1/crawl" \
     -H "Content-Type: application/json" \
     -d '{
       "urls": [
         "https://example.com/article1",
         "https://example.com/article2"
       ],
       "extract_content": true,
       "include_links": false
     }'
```

## 🏗️ Architecture

### Services
- **Crawler Orchestrator**: Main application with FastAPI and Gradio
- **Crawl4AI Service**: Dedicated web crawling service
- **NocoDB**: Visual database for data storage
- **Redis**: Caching and task queue management
- **Scheduler**: Background task processing

### AI Agents
- **Research Agent**: Uses search and crawling tools
- **Content Analyzer**: Processes and analyzes content
- **Data Curator**: Organizes and structures data
- **Report Generator**: Creates comprehensive reports

### Data Flow
1. User defines research mission or crawling task
2. Research Agent searches for relevant sources
3. Crawler extracts content from identified URLs
4. Content Analyzer processes and analyzes data
5. Data Curator organizes and structures results
6. Report Generator creates final output
7. Results stored in NocoDB for future access

## ⚙️ Configuration

### Environment Variables

```bash
# API Keys
OPENROUTER_API_KEY=your_key_here
EXA_API_KEY=your_key_here
BRAVE_API_KEY=your_key_here

# Service URLs
NOCODB_URL=http://nocodb:8080
REDIS_URL=redis://redis:6379

# Crawler Settings
MAX_CONCURRENT_CRAWLS=5
CRAWL_TIMEOUT=300
USER_AGENT=MultiAgentCrawler/1.0

# LLM Settings
DEFAULT_MODEL=google/gemma-2-9b-it
TEMPERATURE=0.7
MAX_TOKENS=4000
```

### Available LLM Models

- **Gemma 2 9B**: Fast and efficient (default)
- **Gemma 2 27B**: More capable, slower
- **Llama 3.1 8B**: Long context support

## 🛠️ Management Commands

```bash
# Start services
./install.sh start

# Stop services
./install.sh stop

# Restart services
./install.sh restart

# View logs
./install.sh logs

# Check status
./install.sh status

# Clean up (removes all data)
./install.sh clean
```

## 📊 Monitoring

### System Status
Check system health at: http://localhost:8000/api/v1/status

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f crawler-orchestrator
docker-compose logs -f crawl4ai
```

### Performance Metrics
- Redis statistics available via API
- Crawling success rates tracked
- Agent performance monitoring

## 🔧 Development

### Local Development Setup

1. **Install Python dependencies**
   ```bash
   cd crawler-orchestrator
   pip install -r requirements.txt
   ```

2. **Start services individually**
   ```bash
   # Start Redis and NocoDB
   docker-compose up -d redis nocodb
   
   # Run main application locally
   python main.py
   ```

### Adding Custom Agents

1. Create new agent in `src/agents/`
2. Define tools in `src/tools/`
3. Register agent in `crew_orchestrator.py`
4. Update API routes if needed

### Custom Tools Development

Tools must inherit from `crewai_tools.BaseTool`:

```python
class CustomTool(BaseTool):
    name: str = "custom_tool"
    description: str = "Description of what the tool does"
    
    def _run(self, input_param: str) -> str:
        # Tool implementation
        return "Tool result"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Issues**: Report bugs and request features via GitHub Issues
- **Documentation**: Check the `/docs` endpoint for API documentation
- **Logs**: Use `docker-compose logs` for troubleshooting

## 🙏 Acknowledgments

- **Crawl4AI**: Advanced web crawling capabilities
- **CrewAI**: Multi-agent orchestration framework
- **Exa**: Semantic search API
- **Brave Search**: Privacy-focused search API
- **NocoDB**: Open-source Airtable alternative
- **OpenRouter**: LLM API aggregation service
